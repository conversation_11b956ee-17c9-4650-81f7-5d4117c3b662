import { smartNavigate, handlePageInitialBlocks, simulateHumanRandomClicks, simulateHumanBehavior, compatEvaluate } from './browser.js';
import config from '../config.js';
import { info, warn, error } from './logger.js';
import { getModelConfig } from '../models-config.js';

/**
 * 封装了与 页面交互的所有操作，
 * 如输入文本、点击按钮、设置模型和温度等。
 */
export class AppInjector {
  /**
   * @param {import('playwright').Page} page Playwright 页面对象
   * @param {object} [options={}] 配置选项
   * @param {string} [options.model] 模型 ID。
   * @param {number} [options.temperature] 温度值。
   * @param {string} [options.systemPrompt] 系统指令。
   * @param {string} [options.historyPrompt] 整合的指令。
   */
  constructor(page, options = {}) {
    this.page = page;
    this.options = options;
    this.modelMapping = Object.fromEntries(
      Object.entries(config.models).map(([key, model]) => [key, model.displayName])
    );
  }
  /**
   * 查找页面上的主输入框。
   * 增强了选择器和等待逻辑，以提高在动态加载页面上的成功率。
   * @returns {Promise<import('playwright').Locator|null>} 输入框的定位器。
   */
  async findInputElement() {
    try {
      // 基于提供的HTML结构，查找textarea输入框
      const selectors = [
        '.prompt-input-wrapper .textarea-wrapper textarea'
      ];

      for (const selector of selectors) {
        try {
          const element = this.page.locator(selector).first();
          await element.waitFor({ timeout: 1000 });
          if (await element.isVisible()) {
            info(`找到输入框: ${selector}`);
            return element;
          }
        } catch (err) {
          // 继续尝试下一个选择器
          continue;
        }
      }

      warn('未找到输入框元素');
      return null;
    } catch (err) {
      error('查找输入框时出错:', err);
      return null;
    }
  }

  /**
   * 查找页面上的发送按钮。
   * 基于HTML结构查找带有箭头图标的提交按钮。
   * @returns {Promise<import('playwright').Locator|null>} 发送按钮的定位器。
   */
  async findSendButton() {
    try {
      // 基于提供的HTML结构，查找发送按钮
      const selectors = [
        '.textarea-wrapper .input-icon',
      ];

      for (const selector of selectors) {
        try {
          const element = this.page.locator(selector).first();
          await element.waitFor({ timeout: 1000 });
          if (await element.isVisible() && await element.isEnabled()) {
            info(`找到发送按钮: ${selector}`);
            return element;
          }
        } catch (err) {
          // 继续尝试下一个选择器
          continue;
        }
      }

      warn('未找到发送按钮元素');
      return null;
    } catch (err) {
      error('查找发送按钮时出错:', err);
      return null;
    }
  }

  /**
   * 查找模型选择按钮。
   * @returns {Promise<import('playwright').Locator|null>} 模型选择按钮的定位器。
   */
  async findModelButton() {
    try {
      const selectors = [
        '.models-selected'
      ];

      for (const selector of selectors) {
        try {
          const element = this.page.locator(selector).first();
          await element.waitFor({ timeout: 5000 });
          if (await element.isVisible()) {
            info(`找到模型选择按钮: ${selector}`);
            return element;
          }
        } catch (err) {
          continue;
        }
      }

      warn('未找到模型选择按钮');
      return null;
    } catch (err) {
      error('查找模型选择按钮时出错:', err);
      return null;
    }
  }
 

  /**
   * 查找指定的模型选项。
   * @param {string} modelName 模型名称
   * @returns {Promise<import('playwright').Locator|null>} 模型选项的定位器。
   */
  async findModelOption(modelName) {
    try {
      // 从 models-config.js 中获取模型配置
      const modelConfig = getModelConfig(modelName);
      if (!modelConfig) {
        warn(`未找到模型配置: ${modelName}`);
        return null;
      }

      const targetLabel = modelConfig.label;
      info(`查找模型选项: ${modelName} (标签: ${targetLabel})`);

      // 等待模型列表加载
      await this.page.waitForSelector('.models-list', { timeout: 5000 });

      // 查找所有模型选项
      const modelElements = this.page.locator('.models-list .model');
      const count = await modelElements.count();

      for (let i = 0; i < count; i++) {
        const modelElement = modelElements.nth(i);

        // 获取模型文本标签
        const textElement = modelElement.locator('.text').first();
        if (await textElement.isVisible()) {
          const text = await textElement.textContent();
          if (text && text.trim() === targetLabel) {
            info(`找到匹配的模型选项: ${targetLabel}`);
            return modelElement;
          }
        }
      }

      warn(`未找到模型选项: ${modelName} (标签: ${targetLabel})`);
      return null;
    } catch (err) {
      error('查找模型选项时出错:', err);
      return null;
    }
  }

  /**
   * 使用剪贴板方式快速填入长文本（最快的方法）。
   * @param {string} message 要发送的消息。
   * @returns {Promise<boolean>} 是否填充成功。
   */
  async fillMessageWithClipboard(message) {
    try {
      // 使用剪贴板API设置文本
      await compatEvaluate(this.page, (text) => {
        navigator.clipboard.writeText(text);
      }, message);

      // 等待剪贴板设置完成
      await this.page.waitForTimeout(100);

      // 使用 Ctrl+V 粘贴
      await this.page.keyboard.press('Control+v');


      return true;
    } catch (err) {
      error('剪贴板填充失败:', err);
      return false;
    }
  }

  /**
   * 在输入框中填入消息。
   * @param {string} message 要发送的消息。
   * @param {"auto"|"clipboard"|"direct"} fillMethod 填充方法。。
   * @returns {Promise<boolean>} 是否填充成功。
   */
  async fillMessage(message, fillMethod = 'auto') {
    const inputElement = await this.findInputElement();
    try {
      if (config.enableHumanSimulation && inputElement) {
        await simulateHumanRandomClicks(this.page, { referenceElement: inputElement });
      }
      // 根据方法选择填充策略
      let selectedMethod = fillMethod;
      if (fillMethod === 'auto') {
        // 自动选择最佳方法
        if (message.length > 2000 || !inputElement) {
          selectedMethod = 'clipboard';  // 超长文本用剪贴板
        } else {
          selectedMethod = 'direct';     // 短文本直接填充
        }
      }

      info(`使用 ${selectedMethod} 方法填充消息（${message.length} 字符）...`);

      switch (selectedMethod) {
        case 'clipboard':
          // 先清空输入框
          if (inputElement) {
            await inputElement.clear();
            // 点击输入框确保焦点
            await inputElement.click();
          }
          const clipboardResult = await this.fillMessageWithClipboard(message);
          // 检查剪贴板填充是否成功
          if (inputElement) {
            const currentValue = await inputElement.inputValue();
            if (!currentValue || currentValue.trim() === '') {
              info('剪贴板填充失败，使用直接填充作为备用方案...');
              await inputElement.fill(message);
              info('消息填充完成（备用直接填充）。');
              return true;
            }
          }

          info('消息填充完成（剪贴板方式）。');
          return clipboardResult;

        case 'direct':
        default:
          await inputElement.clear();
          await inputElement.fill(message);
          info('消息填充完成（直接填充）。');
          return true;
      }
    } catch (err) {
      error('填充消息失败:', err);
      return false;
    }
  }

  /**
   * 点击发送按钮发送消息。
   * @returns {Promise<boolean>} 是否发送成功。
   */
  async sendMessage() {
    if (config.enableHumanSimulation) {
      //await simulateHumanBehavior(this.page, { includeScrolling: false, duration: 1500 });
      const inputElement = await this.findInputElement();
      if (inputElement) {
        await simulateHumanRandomClicks(this.page, { referenceElement: inputElement });
      }
    }
    const sendButton = await this.findSendButton();

    // 如果找不到发送按钮，尝试使用回车键发送
    if (!sendButton) {
      info('无法找到发送按钮，尝试使用回车键发送...');
      try {
        await this.page.keyboard.press('Enter');
        info('已使用回车键发送消息。');
        return true;
      } catch (err) {
        error('使用回车键发送失败:', err);
        throw new Error('无法找到可用的发送按钮，且回车键发送也失败。');
      }
    }

    try {
      await sendButton.click();
      info('消息已发送。');
      return true;
    } catch (err) {
      error('点击发送按钮失败:', err);
      throw new Error('点击发送按钮失败');
    }
  }
  /**
   * 设置 AI 模型。
   * @param {string} modelName 模型 ID (例如 'gemini-2.5-pro')。
   * @returns {Promise<boolean>} 是否设置成功。
   */
  async setModel(modelName) {
    try {
      info(`开始设置模型: ${modelName}`);

      // 步骤1: 点击模型选择按钮
      const modelButton = await this.findModelButton();
      if (!modelButton) {
        throw new Error('无法找到模型选择按钮');
      }

      await modelButton.click();
      info('已点击模型选择按钮');

      // 等待搜索框出现
      await this.page.waitForTimeout(500);

      // 步骤3: 点击匹配的模型选项
      const modelOption = await this.findModelOption(modelName);
      if (!modelOption) {
        throw new Error(`无法找到模型选项: ${modelName}`);
      }

      await modelOption.click();
      info(`已选择模型: ${modelName}`);

      // 等待模型选择完成
      await this.page.waitForTimeout(500);

      return true;
    } catch (err) {
      error('设置模型失败:', err);
      return false;
    }
  }

  async loadPage() {
    try {

      await smartNavigate(this.page, config.app.url, { timeout: config.app.pageTimeout });

      await this.page.waitForSelector('body', { timeout: 30000 });

      // 处理页面初始化阻挡元素（主要是Cloudflare验证）
      await handlePageInitialBlocks(this.page);

      // 如果启用了人类行为模拟，在导航后进行随机点击
      if (config.enableHumanSimulation) {
        info('启用了人类行为模拟，等待几百毫秒后进行随机点击...');
        await this.page.waitForTimeout(Math.floor(Math.random() * 300) + 200); // 200-500ms 随机等待
        await simulateHumanRandomClicks(this.page);
      }
      return true;
    } catch (err) {
      error('页面加载超时或失败:', err);
      return false;
    }
  }

  /**
   * 完整处理流程：加载页面、设置参数、填写并发送消息。
   * @param {string} message 要发送的消息。

   * @returns {Promise<boolean>} 整个流程是否成功。
   */
  async processMessage() {
    info('开始处理消息...');
    //if (this.options.model) await this.setModel(this.options.model);
    if (!await this.fillMessage(this.options.historyPrompt, "auto")) throw new Error('消息填写失败。');
    await this.page.waitForTimeout(500);
    if (!await this.sendMessage()) throw new Error('消息发送失败。');
    info('消息发送成功，等待网络拦截获取响应。');
    return true;
  }
}