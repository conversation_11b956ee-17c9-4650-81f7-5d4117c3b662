# Docker Compose noVNC 配置文件
# 用于启用 noVNC Web 界面远程查看浏览器界面
# 使用方法: docker-compose -f docker-compose.yml -f docker-compose.novnc.yml up -d

services:
  genspark2api:
    # 启用 noVNC 端口映射
    ports:
      - "6080:6080"  # noVNC Web 界面端口
      - "5900:5900"  # VNC 直连端口

    # noVNC 相关环境变量
    environment:
      - ENABLE_NOVNC=true

    # 挂载修改后的启动脚本
    volumes:
      - "./start-with-xvfb-novnc.sh:/app/start-with-xvfb-novnc.sh"

    # 修改启动脚本以启用 noVNC
    # 注意：如果同时使用 override.yml (开发模式)，会自动使用开发版本的 noVNC 脚本
    command: ["/app/start-with-xvfb-novnc.sh"]
