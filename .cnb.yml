main:
  push:
    - docker:
        image: node:18
      imports: https://cnb.cool/godgodgame/oci-private-key/-/blob/main/envs.yml
      stages:
        - name: 环境检查
          script: echo $GITHUB_TOKEN_GK && echo $GITHUB_TOKEN && node -v && npm -v
        - name: 将pro分支同步更新到github的main分支
          script: git push https://$GITHUB_TOKEN_GK:$<EMAIL>/zhezzma/genspark2api.git HEAD:main





$:
  vscode:
    - docker:
        #image: node:20
        build:
          dockerfile: .ide/Dockerfile
          by:
            - .ide/settings.json
            - .ide/cnb-init-from
            - .ide/cnb-init-from-without-lfs
            - .ide/gitconfig
      env:
        CNB_WELCOME_EXECUTE_COMMAND: echo "Welcome to My CNB!"
      services:
        - vscode
        - docker
      # 开发环境启动后会执行的任务
      stages:             
        - name: 安装依赖和构建
          script: |
            cd /workspace
            npm config set registry https://registry.npmmirror.com
            npm install 
            npx patchright install chromium
            npx playwright install chromium
            chmod +x ./dev-container-*.sh
            ./dev-container-start.sh > ./dev-container.log 2>&1 &