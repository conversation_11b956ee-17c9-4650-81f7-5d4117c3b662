import { info, warn, error } from './logger.js';

/**
 * 检查并返回非流式错误。
 * @param {string} responseText - 响应文本。
 * @returns {{type: 'error', content: string}|null}
 */
function getResponseError(responseText) {
  if (responseText.startsWith('<!DOCTYPE html>')) {
    return {
      type: 'error',
      content: "被cf盾了" // Blocked by Cloudflare
    };
  }
  if (responseText.startsWith('{"error"') && responseText.endsWith('}')) {
    try {
      const errorObj = JSON.parse(responseText);
      return {
        type: 'error',
        content: errorObj.error
      };
    } catch (e) {
      return null;
    }
  }
  return null;
}

/**
 * 解析流式响应，处理 Server-Sent Events (SSE) 格式的数据。
 *
 * 响应格式示例:
 * data: {"id": "bd835ea7-c64c-4ec1-a798-dd9ce1875013", "type": "project_start"}
 * data: {"message_id": "d29385d1-84e3-4df2-a6df-97ee8125f89b", "field_name": "session_state.answer", "delta": "你好", "project_id": null, "type": "message_field_delta"}
 *
 * @param {string} buffer - 当前累积的响应字符串缓冲区。
 * @returns {{parsedItems: Array<{type: 'text'| 'thinking' | 'done' | 'error' | 'unknown', content: any}>, remainingBuffer: string}}
 *          返回一个包含已解析项目和剩余缓冲区的对象。
 */
export function parseResponse(buffer) {
  const errorResponse = getResponseError(buffer);
  if (errorResponse) {
    return { parsedItems: [errorResponse], remainingBuffer: '' };
  }

  const parsedItems = [];
  let currentBuffer = buffer;

  while (true) {
    const newlineIndex = currentBuffer.indexOf('\n');
    if (newlineIndex === -1) {
      break;
    }

    const line = currentBuffer.substring(0, newlineIndex);
    currentBuffer = currentBuffer.substring(newlineIndex + 1);

    if (!line.trim()) {
      continue;
    }

    // 处理 SSE 格式: data: {...}
    if (line.startsWith('data: ')) {
      const jsonStr = line.substring(6); // 移除 "data: " 前缀
      try {
        const result = JSON.parse(jsonStr);

      // 根据参考代码的逻辑处理不同类型的消息

      // 处理 thinking 类型的增量更新
      if (result.field_name === "session_state.answerthink" && result.type === "message_field_delta") {
        parsedItems.push({
          type: 'thinking',
          content: result.field_value || result.delta,
        });
      }

      // 处理普通文本的增量更新
      else if (result.field_name === "session_state.answer" && result.type === "message_field_delta") {
        parsedItems.push({
          type: 'text',
          content: result.field_value || result.delta
        });
      }

      // 处理可能的错误情况
      else if (result.type === "message_result" && result.id === "") {
        parsedItems.push({
          type: 'text',
          content: result.content
        });
      }

      // 其他类型的消息暂时忽略（如 project_start, project_field, message_field 等）
      // 这些通常是元数据，不需要发送给客户端

      } catch (e) {
        warn(`JSON解析失败: "${jsonStr}"`, e);
        parsedItems.push({
          type: 'error',
          content: `解析错误: ${e.message}`
        });
      }
    }
  }

  return {
    parsedItems,
    remainingBuffer: currentBuffer
  };
}


// --- OpenAI 格式化工具函数 (保持不变) ---

/**
 * 创建 OpenAI 格式的流式响应块 (chunk)。
 * @param {string} content - 文本内容。
 * @param {'thinking' | 'text'} type - 内容的类型。
 * @param {string} [model] - 模型名称。
 * @returns {object}
 */
export function createStreamResponse(content, type, model) {
  return {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion.chunk',
    created: Math.floor(Date.now() / 1000),
    model,
    choices: [{
      index: 0,
      delta: {
        content: content || '',
        type: type || 'text',
      },
      logprobs: null,
      finish_reason: null
    }]
  };
}

/**
 * 创建 OpenAI 格式的错误响应。
 * @param {string} message - 错误消息。
 * @param {string} [model] - 模型名称。
 * @returns {object}
 */
export function createErrorResponse(message, model) {
  return {
    object: 'error',
    message,
    type: 'invalid_request_error',
    model,
  };
}

/**
 * 创建 OpenAI 格式的非流式（完整）响应。
 * @param {string} content - 完整的响应内容。
 * @param {string} [model] - 模型名称。
 * @returns {object}
 */
export function createNonStreamResponse(content, model) {
  return {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion',
    created: Math.floor(Date.now() / 1000),
    model,
    choices: [{
      index: 0,
      message: {
        role: 'assistant',
        content
      },
      finish_reason: 'stop'
    }],
    usage: {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    }
  };
}