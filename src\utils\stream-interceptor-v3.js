import { info, error } from './logger.js'
import config from '../config.js'
import { modifyBodyForModel } from './body-modifier.js'

/**
 *  使用的window对象存储,然后进行轮询
 * window.fetch 在firefox中拦截不到,所以这个也是无效的..但是利用存储轮询的思路是没问题的
 */
export class StreamInterceptorV3 {
  /** @readonly */
  TARGET_URL_PATTERNS = config.targetUrlPatterns;

  /**
   * @param {import('playwright').Page} page Playwright 页面对象
   * @param {function(string): void} onStreamChunk 流数据块回调
   * @param {function(): void} onStreamEnd 流结束回调
   * @param {Object} opts 选项参数
   * @param {string} opts.model 模型名称，用于替换请求中的modelAId
   */
  constructor(page, onStreamChunk, onStreamEnd, opts = {}) {
    this.page = page;
    this.onStreamChunk = onStreamChunk;
    this.onStreamEnd = onStreamEnd;
    this.opts = opts;
    this.uniqueId = `interceptor_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    this.modifyBodyCallbackName = `__modifyBody_${this.uniqueId}`;
    this.isInjected = false;
    this.isActive = false;

  }

  /**
   * 将拦截器脚本和所有配置一次性注入到页面中。
   * @private
   */
  async _inject() {
    if (this.isInjected) return;

    info(`[Interceptor] 注入流式拦截脚本 (ID: ${this.uniqueId})...`);

    // 暴露修改body的函数
    await this.page.exposeFunction(this.modifyBodyCallbackName, (bodyStr) => {
      return modifyBodyForModel(bodyStr, this.opts.model, config.modelsAIds);
    });

    const injectionPayload = {
      TARGET_URL_PATTERNS: this.TARGET_URL_PATTERNS,
      uniqueId: this.uniqueId,
      compatMode: true,
      modifyBodyCallbackName: this.modifyBodyCallbackName,
    };
    const browserScript = this._getBrowserScript();
    // 使用兼容函数确保正确的执行上下文
    await this.page.evaluate(browserScript, injectionPayload, false);

    // 启动轮询来检查收集到的数据
    this._startPolling();
    this.isInjected = true;
    info(`[Interceptor] 脚本注入已完成 (ID: ${this.uniqueId})`);
  }

  /**
   * 启动轮询来检查收集到的数据
   * @private
   */
  _startPolling() {
    if (this.pollingInterval) return;

    this.pollingInterval = setInterval(async () => {
      try {
        const result = await this.page.evaluate((uniqueId) => {
          const key = `__streamData_${uniqueId}`;
          const data = window[key];
          if (data && data.chunks && data.chunks.length > 0) {
            const chunks = [...data.chunks];
            const ended = data.ended;
            // 清空已处理的数据
            data.chunks = [];
            return { chunks, ended };
          }
          return null;
        }, this.uniqueId, false);

        if (result) {
          // 处理收集到的数据块
          for (const chunk of result.chunks) {
            this.onStreamChunk(chunk);
          }

          // 如果流已结束
          if (result.ended) {
            this.onStreamEnd();
            this._stopPolling();
          }
        }
      } catch (err) {
        // 如果页面已关闭，停止轮询
        if (err.message.includes('Target page, context or browser has been closed')) {
          this._stopPolling();
          return;
        }
        error(`[Interceptor] 轮询时出错: ${err.message}`);
      }
    }, 100); // 每100ms检查一次
  }

  /**
   * 停止轮询
   * @private
   */
  _stopPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
  }

  async activate() {
    if (this.isActive) return;
    await this._inject();

    info(`[Interceptor] 激活拦截器 (ID: ${this.uniqueId})...`);
    await this.page.evaluate(() => {
      if (window.__streamInterceptor) {
        window.__streamInterceptor.activate();
      }
    }, undefined, false);
    this.isActive = true;
    info(`[Interceptor] 拦截器已激活 (ID: ${this.uniqueId})`);
  }

  async deactivate() {
    if (!this.isActive) return;

    info(`[Interceptor] 停用拦截器 (ID: ${this.uniqueId})...`);
    try {
      // 停止轮询
      this._stopPolling();

      await this.page.evaluate(() => {
        if (window.__streamInterceptor) {
          window.__streamInterceptor.deactivate();
        }
      }, undefined, false);
      this.isActive = false;
      info(`[Interceptor] 拦截器已停用 (ID: ${this.uniqueId})`);
    } catch (err) {
      error(`[Interceptor] 停用时发生错误 (可能页面已关闭): ${err.message}`);
    }
  }


  /**
   * 返回要注入到浏览器页面的脚本函数
   * @returns {function}
   * @private
   */
  _getBrowserScript() {
    return (payload) => {
      if (window.__streamInterceptor) {
        if (typeof window.__streamInterceptor.deactivate === 'function') {
          window.__streamInterceptor.deactivate();
        }
      }

      const {
        TARGET_URL_PATTERNS,
        uniqueId,
        modifyBodyCallbackName,
      } = payload;

      const originalFetch = window.fetch;
      const modifyBodyCallback = window[modifyBodyCallbackName];

      const dataKey = `__streamData_${uniqueId}`;
      window[dataKey] = { chunks: [], ended: false };

      const onChunkCallback = (chunk) => {
        window[dataKey].chunks.push(chunk);
      };

      const onEndCallback = () => {
        window[dataKey].ended = true;
      };
      let interceptorActive = false;

      window.__streamInterceptor = {
        activate: () => {
          if (interceptorActive) return;
          interceptorActive = true;

          // 拦截 fetch API
          window.fetch = async function (input, init = {}) {
            const url = typeof input === 'string' ? input : input.url;
            const urlString = url ? url.toString() : '';
            const isTargetRequest = TARGET_URL_PATTERNS.some(pattern => urlString.includes(pattern));

            if (isTargetRequest && interceptorActive) {
              try {
                // 修改请求body中的模型ID
                if (init && init.body && modifyBodyCallback) {
                  const originalBody = init.body;
                  const modifiedBody = await modifyBodyCallback(originalBody);
                  if (modifiedBody !== originalBody) {
                    console.log('🔄 [Interceptor] 已修改请求body中的模型ID');
                    init = { ...init, body: modifiedBody };
                  }
                }

                const response = await originalFetch.call(this, input, init);

                if (!response.body) {
                  return response;
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullResponseText = '';
                let streamEnded = false;

                const finalizeStream = () => {
                  if (streamEnded) return;
                  streamEnded = true;
                  if (onEndCallback) {
                    onEndCallback();
                  }
                };

                // 创建一个新的 ReadableStream 来代理原始流
                const stream = new ReadableStream({
                  start(controller) {
                    function pump() {
                      return reader.read().then(({ done, value }) => {
                        if (done) {
                          finalizeStream();
                          controller.close();
                          return;
                        }

                        const chunk = decoder.decode(value, { stream: true });
                        fullResponseText += chunk;

                        // 发送数据块回调
                        if (onChunkCallback) {
                          onChunkCallback(chunk);
                        }

                        controller.enqueue(value);
                        return pump();
                      });
                    }

                    return pump();
                  }
                });

                // 返回一个新的 Response 对象，使用我们的代理流
                return new Response(stream, {
                  status: response.status,
                  statusText: response.statusText,
                  headers: response.headers
                });

              } catch (err) {
                return originalFetch.call(this, input, init);
              }
            }

            return originalFetch.call(this, input, init);
          };
        },

        deactivate: () => {
          if (!interceptorActive) return;
          window.fetch = originalFetch;
          interceptorActive = false;
        }
      };
    };
  }
}
