/**
 * 修改请求body中的模型ID
 * @param {string} bodyStr 原始body字符串
 * @param {string} model 目标模型名称
 * @param {Array} modelsAIds 模型配置数组
 * @returns {string} 修改后的body字符串
 */
export function modifyBodyForModel(bodyStr, model, modelsAIds) {
  if (!model ) {
    return bodyStr;
  }
  // 从数组中查找对应的模型配置
  try {
    const body = JSON.parse(bodyStr);
    body.extra_data.models[0] = model;
    return JSON.stringify(body);
  } catch (err) {
    console.error('[modifyBodyForModel] JSON解析失败:', err);
    return bodyStr;
  }
}
