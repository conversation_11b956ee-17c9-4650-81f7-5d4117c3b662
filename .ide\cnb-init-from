#!/usr/bin/env bash

function _log() {
  local timestamp=$(date +'%Y-%m-%d %H:%M.%S')
  local SET_COLOR='\033[32m'
  local CLEAR_COLOR='\033[0m'
  printf "$SET_COLOR%s \$$CLEAR_COLOR %s\n" "$timestamp" "$*"
}

# 检查变量是否空仓
REF_COUNT=$(git for-each-ref refs/tags/ refs/heads/ refs/remotes/ |wc -l)

if [[ $REF_COUNT -gt 0 ]]; then
    _log "warning: 仅空仓才需迁移，当前仓库不是空仓！"
    exit 2
fi

# 检查变量是否设置
if [[ -z "$1" ]]; then
  echo "warning: git url 未指定."
  echo "示例: cnb-init-from https://xxxx.com/your-code.git"
  exit 1
fi

rm -rf /tmp/empty
mkdir /tmp/empty
cd /tmp/empty

_log 'git config --global credential.helper "store --file=/workspace/.git/.git-credentials"'
      git config --global credential.helper "store --file=/workspace/.git/.git-credentials"

_log "git clone --bare "$1" ."
      git clone --bare "$1" .
      ret=$?; [ $ret -ne 0 ] && exit $ret;

_log "git lfs fetch --all origin"
      git lfs fetch --all origin
      ret=$?; [ $ret -ne 0 ] && exit $ret;

_log "git remote add this ${CNB_WEB_PROTOCOL}://${CNB_WEB_HOST}/${CNB_REPO_SLUG}.git"
      git remote add this ${CNB_WEB_PROTOCOL}://cnb:${CNB_TOKEN}@${CNB_WEB_HOST}/${CNB_REPO_SLUG}.git

# _log "git lfs migrate import --everything --above=500Mb"
#       git lfs migrate import --everything --above=500Mb
#       ret=$?; [ $ret -ne 0 ] && exit $ret;

_log "git push --mirror this"
      git push --mirror this
      ret=$?; [ $ret -ne 0 ] && exit $ret;

_log "git rev-list --count --all"
      git rev-list --count --all

_log "git lfs push --all this"
      git lfs push --all this
      ret=$?; [ $ret -ne 0 ] && exit $ret;

_log "迁移成功，检查迁移效果..."
_log "cd /workspace"
      cd /workspace

_log "git fetch"
      git fetch

_log "git checkout FETCH_HEAD"
      git checkout FETCH_HEAD

_log "git status"
      git status

if [[ -f ".ide/Dockerfile" ]]; then
  _log "检测到当前仓库有定制开发环境(.ide/Dockerfile)"

  _log "当前环境 3s 后自动关闭，请重新启动开发环境"
  _log "3"
  sleep 1
  _log "2"
  sleep 1
  _log "1"
  sleep 1
  _log "已销毁"
  kill 1
fi

if [[ -f ".cnb.yml" ]]; then
  _log "检测到当前仓库有可能定制了开发环境(.cnb.yml)"

  _log "当前环境 3s 后自动关闭，请重新启动开发环境"
  _log "3"
  sleep 1
  _log "2"
  sleep 1
  _log "1"
  sleep 1
  _log "已销毁"
  kill 1
fi

_log "工作区已经同步更新"