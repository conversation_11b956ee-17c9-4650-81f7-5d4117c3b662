import { AppInjector } from './app-injector.js';
import { getPageFromPool, releasePageToPool, smartNavigate, simulateHumanRandomClicks, getBrowserEnvironmentManager } from './browser.js';
import { StreamInterceptor } from './stream-interceptor.js';
import { StreamInterceptorV2 } from './stream-interceptor-v2.js';
import { StreamInterceptorV3 } from './stream-interceptor-v3.js';
import { StreamInterceptorV4 } from './stream-interceptor-v4.js';
import { parseResponse, createStreamResponse, createErrorResponse, createNonStreamResponse } from './response-parser.js';
import config from '../config.js';
import { info, warn, error } from './logger.js';
import fs from 'node:fs';

const MAX_RETRIES = 1;
const RETRY_DELAY_MS = 1000; // 增加了重试延迟，给页面更多反应时间

let streamInterceptor = StreamInterceptorV2;
if (config.browser.type === 'patchright' || config.browser.type === 'chromium') {
  streamInterceptor = StreamInterceptor;
}

/**
 * 保存 cookies 到文件
 * @param {object} options - 请求选项，包含 appToken
 */
async function saveCookies(options = {}) {
  try {
    return;
    const appToken = options.appToken || null;
    const manager = getBrowserEnvironmentManager();
    const userEnv = manager.getUserEnvironment(appToken);

    if (userEnv.context) {
      const newCookies = await userEnv.context.cookies();
      if (newCookies.length > 0) {
        fs.writeFileSync(config.cookieFile, JSON.stringify(newCookies, null, 2));
        info(`成功将 ${newCookies.length} 个 Cookie 保存到 ${config.cookieFile}`);
        info('主要 Cookie 名称:');
        newCookies.slice(0, 5).forEach(cookie => {
          info(`  - ${cookie.name} (域: ${cookie.domain})`);
        });
      } else {
        error('未能获取到任何 Cookie，请确保已成功登录。');
      }
    }
  } catch (cookieErr) {
    error('保存 Cookie 时出错:', cookieErr);
  }
}
/**
 * 核心处理逻辑，封装了重试和页面交互。
 * @param {string} prompt - 用户提示。
 * @param {object} options - 请求选项。
 * @param {function} responseHandler - 处理响应的回调函数。
 * @returns {Promise<any>} 响应处理函数返回的结果。
 */
async function coreProcessor(options, responseHandler) {
  let page = null;
  let lastError = null;
  try {
    // 从 options 中获取 appToken
    const appToken = options.appToken || null;
    page = await getPageFromPool(appToken);
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        info(`🌐 [尝试 ${attempt}/${MAX_RETRIES}] 开始处理请求...`);
        if (appToken) {
          info(`使用 APP_TOKEN token: ${appToken.substring(0, 10)}...`);
        }
        const result = await responseHandler(page);
        await releasePageToPool(page, appToken);
        return result;
      } catch (err) {
        lastError = err;
        warn(`❌ [尝试 ${attempt}/${MAX_RETRIES}] 失败: ${err.message}`);
        if (attempt < MAX_RETRIES) {
          info(`检测到可重试的错误（权限或页面状态），将在 ${RETRY_DELAY_MS}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
        } else {
          error(`错误不可重试或已达到最大重试次数，将抛出异常。`);
          throw err; // 抛出错误，终止循环
        }
      }
    }
  } catch (err) {
    error('处理请求最终失败:', err);
    if (page) {
      // 从 options 中获取 appToken 用于释放页面
      const appToken = options?.appToken || null;
      await releasePageToPool(page, appToken);
    }
    throw lastError || err;
  }
}

/**
 * 流式处理请求。
 * @param {ReadableStreamDefaultController} controller - 流控制器。
 * @param {object} options - 请求选项。
 */
export async function processRequest(controller, options = {}) {
  const model = options.model || config.app.defaultModel;

  try {
    await coreProcessor(options, async (page) => {
      return new Promise(async (resolve, reject) => {
        let streamEnded = false;
        let interceptor = null;

        // 关键改动：为每个请求维护一个独立的 buffer
        let responseBuffer = "";

        const finalize = (err) => {
          if (streamEnded) return;
          streamEnded = true;
          if (interceptor) {
            interceptor.deactivate().catch(error);
          }
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        };

        const onStreamChunk = (chunk) => {
          if (streamEnded) return;

          // 1. 将新数据块追加到缓冲区
          responseBuffer += chunk;

          // 2. 调用新的解析器
          const { parsedItems, remainingBuffer } = parseResponse(responseBuffer);

          // 3. 更新缓冲区，保留未解析的部分
          responseBuffer = remainingBuffer;

          // 4. 处理所有已解析的项目
          if (parsedItems.length > 0) {
            for (const item of parsedItems) {
              if (item.type === 'error') {
                return finalize(new Error(item.content));
              }
              if (item.type === 'done') {
                // 如果解析器发来结束信号，我们可以提前结束
                onStreamEnd();
                return;
              }
              // 发送所有内容，包括空白字符和换行符，因为它们都是有意义的
              // 只过滤掉 undefined 或 null
              if (item.content !== undefined && item.content !== null) {
                controller.enqueue(`data: ${JSON.stringify(createStreamResponse(item.content, item.type, model))}\n\n`);
              }
            }
          }
        };

        const onStreamEnd = () => {
          if (streamEnded) return;
          info('流已结束，发送 [DONE] 信号。');
          controller.enqueue('data: [DONE]\n\n');
          controller.close();
          finalize();
        };
        try {
          if (process.env.USE_INTERCEPTOR_V4 === 'true') {
            interceptor = new StreamInterceptorV4(page, onStreamChunk, onStreamEnd, options);
            await interceptor.activate();
            // V4 模式：直接执行请求，不需要 AppInjector
            info('[V4模式] 跳过页面交互，直接执行 API 请求...');
            await interceptor.executeDirectRequest();
          } else {
            // 传统模式：使用 AppInjector 进行页面交互
            const injector = new AppInjector(page, options);
            await injector.loadPage();
            interceptor = new streamInterceptor(page, onStreamChunk, onStreamEnd, options);
            await interceptor.activate();
            await injector.processMessage();
          }
          setTimeout(() => {
            if (!streamEnded) {
              finalize(new Error('响应超时。'));
            }
          }, config.app.responseTimeout);
        } catch (err) {
          finalize(err);
        }
      });
    });
  } catch (err) {
    if (controller.desiredSize !== null) {
      try {
        controller.enqueue(`data: ${JSON.stringify(createStreamResponse(err.message, 'text', model))}\n\n`);
        controller.enqueue('data: [DONE]\n\n');
        controller.close();
      } catch (e) {
        error('关闭流控制器时出错:', e);
      }
    }
  } finally {
    // 保存 cookies
    await saveCookies(options);
  }

}

/**
 * 非流式（同步）处理请求。
 * @param {object} options - 请求选项。
 * @returns {Promise<object>} OpenAI 格式的响应对象。
 */
export async function processRequestSync(options = {}) {
  const model = options.model || config.app.defaultModel;

  try {
    return await coreProcessor(options, async (page) => {
      return new Promise(async (resolve, reject) => {
        let collectedContent = '';
        const responseListener = async (response) => {
          if (response.url().includes('GenerateContent')) {
            try {
              const text = await response.text();
              const parsedData = parseResponse(text);
              if (parsedData?.permissionError) {
                return reject(new Error(parsedData.content));
              }
              if (Array.isArray(parsedData)) {
                collectedContent += parsedData
                  .filter(item => item.type === 'text')
                  .map(item => item.content)
                  .join('');
              }
              page.removeListener('response', responseListener);
              resolve(createNonStreamResponse(collectedContent, model));
            } catch (err) {
              reject(err);
            }
          }
        };
        page.on('response', responseListener);
        try {
          const injector = new AppInjector(page, options);
          await injector.loadPage();
          await injector.processMessage();
          setTimeout(() => {
            page.removeListener('response', responseListener);
            reject(new Error('响应超时。'));
          }, config.app.responseTimeout);
        } catch (err) {
          page.removeListener('response', responseListener);
          reject(err);
        }
      });
    });
  } catch (err) {
    return createErrorResponse(err.message, model);
  } finally {
    // 保存 cookies
    await saveCookies(options);
  }
}