import { newInjectedContext } from 'fingerprint-injector';
import { FingerprintGenerator } from 'fingerprint-generator';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import config from './config.js';
import { loadCookies, waitForUserInput } from './utils/common-utils.js';
import { info, error } from './utils/logger.js';
import { createBrowser, createBrowserContext, handlePageInitialBlocks } from './utils/browser.js';

/**
 * 启动一个非无头浏览器，引导用户手动登录   并保存 Cookie。
 */
async function login() {
    info('启动浏览器以进行手动登录...');
    config.browser.type = 'patchright';
    const browser = await createBrowser();
    const context = await createBrowserContext(browser)
    // 尝试加载现有 Cookie
    const existingCookies = loadCookies(config.cookieFile, config.cookiesFromEnv);
    if (existingCookies && existingCookies.length > 0) {
        await context.addCookies(existingCookies);
        info(`已加载 ${existingCookies.length} 个现有 Cookie。`);
    }

    const page = await context.newPage();

    try {
        info(`导航至App: ${config.app.url}`);
        await page.goto(config.app.url, { waitUntil: 'networkidle', timeout: 60000000 });
        await page.waitForTimeout(3000); // 等待页面稳定

        // 尝试处理页面初始化阻挡元素（如Cloudflare验证）
        await handlePageInitialBlocks(page);

        info('当前页面 URL:', page.url());
        info('页面标题:', await page.title());

        info('请在浏览器窗口中完成登录操作。');
        info('登录成功并跳转到 App 主界面后，请返回此处按 Enter 键继续...');
        await waitForUserInput();

        info('用户确认登录完成，正在保存 Cookie...');

        // 再次导航以确保在正确的页面上
        try {
            await page.goto(config.app.url, { waitUntil: 'load', timeout: 15000 });
            await page.waitForTimeout(5000); // 等待页面完全加载
        } catch (err) {
            info('导航到主页时出现小问题，但这通常不影响 Cookie 保存。');
        }

        const newCookies = await context.cookies();
        if (newCookies.length > 0) {
            fs.writeFileSync(config.cookieFile, JSON.stringify(newCookies, null, 2));
            info(`成功将 ${newCookies.length} 个 Cookie 保存到 ${config.cookieFile}`);
            info('主要 Cookie 名称:');
            newCookies.slice(0, 5).forEach(cookie => {
                info(`  - ${cookie.name} (域: ${cookie.domain})`);
            });
        } else {
            error('未能获取到任何 Cookie，请确保已成功登录。');
        }

    } catch (err) {
        error('登录过程中发生严重错误:', err);
    } finally {
        info('关闭浏览器。');
        await browser.close();
    }
}

// 使该文件可以直接通过 `node login.js` 运行
const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);
if (path.resolve(__filename) === scriptPath) {
    login().catch(err => error('执行登录脚本失败:', err));
}

export default login;