/**
 * 模型配置数组
 *
 * 这个数组包含了所有支持的模型配置信息，包括图标、标签、描述等。
 *
 * 获取方式：

 */
export const modelsAIds = [{
                name: "gpt-4.1,claude-sonnet-4,gemini-2.5-flash",
                search_model_name: "gpt-4.1,claude-sonnet-4,gemini-2.5-flash",
                icon: "r",
                label: "Mixture-of-Agents",
                full_label: "Mixture-of-Agents",
                description: "Auto mixes best AI models for your task",
                support_images: true,
                support_files: true
            }, {
                name: "gpt-4.1",
                icon: "e",
                label: "GPT-4.1",
                full_label: "Open AI GPT-4.1",
                support_images: true,
                support_files: true
            }, {
                name: "o3",
                icon: "e",
                label: "o3",
                full_label: "Open AI o3",
                support_images: true,
                support_files: true,
                hidden: true
            }, {
                name: "o3-pro",
                icon: "e",
                label: "o3-pro",
                full_label: "Open AI o3-pro",
                support_images: true,
                support_files: true,
                disable_search_web: true
            }, {
                name: "o4-mini-high",
                icon: "e",
                label: "o4-mini-high",
                full_label: "Open AI o4-mini-high",
                support_images: false,
                support_files: true
            }, {
                name: "claude-3-7-sonnet-thinking",
                icon: "t",
                label: "Claude Sonnet 3.7 (Thinking)",
                full_label: "Anthropic Claude Sonnet 3.7 (Thinking)",
                support_images: true,
                support_files: true,
                hidden: true
            }, {
                name: "claude-3-7-sonnet",
                icon: "t",
                label: "Claude Sonnet 3.7",
                full_label: "Anthropic Claude 3.7 Sonnet",
                support_images: true,
                support_files: true,
                hidden: true
            }, {
                name: "claude-sonnet-4-thinking",
                icon: "t",
                label: "Claude Sonnet 4 (Thinking)",
                full_label: "Anthropic Claude Sonnet 4 (Thinking)",
                support_images: true,
                support_files: true,
                hidden: true
            }, {
                name: "claude-sonnet-4",
                icon: "t",
                label: "Claude Sonnet 4",
                full_label: "Anthropic Claude Sonnet 4",
                support_images: true,
                support_files: true
            }, {
                name: "claude-opus-4",
                icon: "t",
                label: "Claude Opus 4",
                full_label: "Anthropic Claude Opus 4",
                support_images: true,
                support_files: true
            }, {
                name: "gemini-2.5-flash",
                icon: "a",
                label: "Gemini 2.5 Flash",
                full_label: "Google Gemini 2.5 Flash",
                support_images: true,
                support_files: true
            }, {
                name: "gemini-2.5-pro",
                icon: "a",
                label: "Gemini 2.5 Pro",
                full_label: "Google Gemini 2.5 Pro",
                support_images: true,
                support_files: true
            }, {
                name: "deep-seek-v3",
                icon: "ee",
                label: "DeepSeek V3",
                full_label: "DeepSeek V3",
                support_images: false,
                support_files: true,
                hidden: true
            }, {
                name: "deep-seek-r1",
                icon: "ee",
                label: "DeepSeek R1",
                full_label: "DeepSeek R1",
                support_images: false,
                support_files: true
            }, {
                name: "kimi-k2-instruct",
                icon: "ne",
                label: "Kimi K2 Instruct",
                full_label: "Kimi K2 Instruct",
                support_images: false,
                support_files: true,
                hidden: true
            }, {
                name: "groq-kimi-k2-instruct",
                icon: "ne",
                label: "Groq Kimi K2 Instruct",
                full_label: "Groq Kimi K2 Instruct",
                support_images: false,
                support_files: true,
                hidden: true
            }, {
                name: "grok-4-0709",
                icon: "ie",
                label: "Grok4 0709",
                full_label: "Grok4 0709",
                support_images: false,
                support_files: true
            }];

/**
 * 根据模型名称获取模型配置
 * @param {string} modelName - 模型名称
 * @returns {object|null} 模型配置对象，如果未找到则返回null
 */
export function getModelConfig(modelName) {
    return modelsAIds.find(config => config.name === modelName) || null;
}

/**
 * 获取所有可见的模型配置（排除hidden为true的模型）
 * @returns {Array} 可见的模型配置数组
 */
export function getVisibleModelConfigs() {
    return modelsAIds.filter(config => !config.hidden);
}

/**
 * 创建模型名称到配置的映射对象（用于兼容旧代码）
 * @returns {Object} 模型名称到配置的映射
 */
export function createModelMapping() {
    const mapping = {};
    modelsAIds.forEach(config => {
        mapping[config.name] = config;
    });
    return mapping;
}
