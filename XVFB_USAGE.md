# 图形界面访问指南

本项目支持在 Docker 容器中使用 Xvfb 运行有头模式的浏览器，并提供 noVNC Web 界面进行远程访问，这样可以在无图形界面的服务器环境中运行需要图形界面的浏览器。

## 🌟 功能特点

- **虚拟显示**：使用 Xvfb 在无图形界面的服务器上运行有头浏览器
- **Web 界面访问**：通过 noVNC 在浏览器中直接查看和控制图形界面
- **跨平台兼容**：支持所有现代浏览器 (Chrome, Firefox, Safari, Edge)
- **实时查看**：实时查看浏览器自动化过程
- **交互控制**：可以通过 Web 界面控制鼠标和键盘
- **无密码访问**：简化的访问方式，适合开发和调试

## 技术架构

### Xvfb (X Virtual Framebuffer)
虚拟显示服务器，在内存中模拟一个图形显示环境，无需实际的显示硬件。这使得可以在无头服务器上运行图形应用程序。

### noVNC 架构
```
浏览器 ←→ noVNC (Web) ←→ websockify ←→ x11vnc ←→ Xvfb (虚拟显示)
                                                        ↑
                                                   应用浏览器
```

- **Xvfb**: 虚拟 X11 显示服务器
- **x11vnc**: VNC 服务器，捕获 X11 显示内容
- **websockify**: WebSocket 到 VNC 的代理
- **noVNC**: HTML5 VNC 客户端

## 🚀 快速开始

### 1. 基本有头模式（推荐）

使用标准的 docker-compose 配置：

```bash
# 构建并启动服务
docker-compose up -d --build

# 查看日志
docker-compose logs -f
```

这种模式下：
- 浏览器运行在有头模式 (`HEADLESS=false`)
- 使用 Xvfb 提供虚拟显示
- 不提供远程访问界面

### 2. 带 noVNC Web 界面模式（推荐用于调试）

如果您需要远程查看浏览器界面进行调试：

```bash
# 生产模式 + noVNC Web 界面
docker-compose -f docker-compose.yml -f docker-compose.novnc.yml up -d --build

# 查看日志
docker-compose -f docker-compose.yml -f docker-compose.novnc.yml logs -f
```

然后在浏览器中访问 `http://localhost:6080` 查看图形界面。

### 3. 开发模式

开发模式自动启用有头模式、详细日志，并运行 `npm run dev`：

```bash
# 开发模式（基本）
docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d --build

# 开发模式 + noVNC Web 界面
docker-compose -f docker-compose.yml -f docker-compose.override.yml -f docker-compose.novnc-dev.yml up -d --build
```

**注意**：开发模式和生产模式使用不同的启动脚本：
- **生产模式**：`start-with-xvfb.sh` (运行 `npm start`)
- **开发模式**：`start-with-xvfb-dev.sh` (运行 `npm run dev`)
- **生产模式 + noVNC**：`start-with-xvfb-novnc.sh`
- **开发模式 + noVNC**：`start-with-xvfb-novnc-dev.sh`

## 🔗 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **noVNC Web 界面** | http://localhost:6080 | 通过浏览器访问图形界面 |
| **应用 API** | http://localhost:7860 | 主要应用服务 |

## 🖥️ 使用 noVNC Web 界面

1. **启动服务**
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.novnc.yml up -d --build
   ```

2. **打开浏览器**
   - 访问 http://localhost:6080
   - 点击 "Connect" 按钮连接到 VNC 服务器

3. **查看界面**
   - 您将看到一个桌面环境 (Fluxbox 窗口管理器)
   - 当应用运行浏览器自动化时，您可以实时看到浏览器窗口

4. **交互控制** (可选)
   - 可以使用鼠标点击和拖拽
   - 可以使用键盘输入
   - 右键菜单可以打开终端等工具

## ⚙️ 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `HEADLESS` | `false` | 是否使用无头模式 |
| `DISPLAY` | `:99` | X11 显示编号 |
| `XVFB_WHD` | `1920x1080x24` | 虚拟显示分辨率和色深 |
| `ENABLE_NOVNC` | `true` | 是否启用 noVNC (在相应配置中) |

## 📋 端口说明

| 端口 | 服务 | 说明 |
|------|------|------|
| 7860 | 应用服务 | 主要 API 服务端口 |
| 6080 | noVNC Web | Web 界面访问端口 |

## 🔧 故障排除

### 容器启动失败
检查日志：
```bash
docker-compose logs genspark2api
```

### noVNC Web 界面连接失败
- 确保容器正在运行: `docker-compose ps`
- 检查端口是否被占用: `netstat -an | grep 6080`
- 查看容器日志: `docker-compose logs genspark2api`

### 界面显示异常
- 刷新浏览器页面
- 检查 VNC 服务器状态
- 重启容器: `docker-compose restart`

### 浏览器无法启动
检查 Xvfb 是否正常运行：
```bash
docker-compose exec genspark2api ps aux | grep Xvfb
```

### 性能问题
如果遇到性能问题，可以调整：
- 降低分辨率：`XVFB_WHD=1280x720x24`
- 增加容器资源限制
- 如果网络较慢，可以在 noVNC 设置中降低图像质量
- 关闭不必要的视觉效果

## 🔒 安全注意事项

- **仅限开发环境**: 当前配置无密码访问，仅适用于开发和调试
- **网络访问**: 默认绑定到所有网络接口，生产环境请限制访问
- **防火墙**: 确保 6080 端口的访问控制

## 📝 注意事项

1. **资源消耗**：有头模式比无头模式消耗更多 CPU 和内存
2. **安全性**：noVNC 连接无密码，仅适用于开发环境
3. **网络**：生产环境建议不要暴露 noVNC 端口
4. **兼容性**：所有浏览器类型（chromium、patchright、firefox、camoufox）都支持

## 🏭 生产环境建议

在生产环境中：
1. 使用基本有头模式（不启用 noVNC）
2. 适当调整资源限制
3. 监控容器资源使用情况
4. 定期清理浏览器缓存和临时文件

## 📚 相关文档

- [noVNC 官方文档](https://github.com/novnc/noVNC)
- [Docker Compose 配置](./docker-compose.novnc.yml)
- [项目主文档](./README.md)
